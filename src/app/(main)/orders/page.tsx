'use client';

import {
  ListOrdered,
  Search,
  X,
  Eye,
  User,
  Package,
  CheckCircle,
  XCircle,
  Loader2,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';

import { OrderQueryParams } from '@/actions/order';
import { MainHeader } from '@/components/main/layout/main-header';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useDebounce } from '@/hooks/use-debounce';
import { formatDuration } from '@/lib/utils';
import { useOrderStore } from '@/store/order/action';

// Using native date formatting to match existing codebase patterns
export default function OrdersPage() {
  const { orders, loading, fetchOrders } = useOrderStore();
  const router = useRouter();

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm, 500);
  const [codeFilter, setCodeFilter] = useState('');
  const [isConfirmedFilter, setIsConfirmedFilter] = useState<
    boolean | undefined
  >(undefined);
  const [userIdFilter, setUserIdFilter] = useState<number | undefined>(
    undefined
  );
  const [templateIdFilter, setTemplateIdFilter] = useState<number | undefined>(
    undefined
  );
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const loadOrders = useCallback(() => {
    const filters: OrderQueryParams = {};

    if (debouncedSearchTerm.trim()) {
      filters.name = debouncedSearchTerm.trim();
    }
    if (codeFilter.trim()) {
      filters.code = codeFilter.trim();
    }
    if (isConfirmedFilter !== undefined) {
      filters.is_confirmed = isConfirmedFilter;
    }
    if (userIdFilter) {
      filters.user_id = userIdFilter;
    }
    if (templateIdFilter) {
      filters.template_id = templateIdFilter;
    }

    fetchOrders(Object.keys(filters).length > 0 ? filters : undefined);
  }, [
    debouncedSearchTerm,
    codeFilter,
    isConfirmedFilter,
    userIdFilter,
    templateIdFilter,
    fetchOrders,
  ]);

  // Load orders on mount and when filters change
  useEffect(() => {
    loadOrders();
  }, [loadOrders]);

  const clearFilters = () => {
    setSearchTerm('');
    setCodeFilter('');
    setIsConfirmedFilter(undefined);
    setUserIdFilter(undefined);
    setTemplateIdFilter(undefined);
  };

  const hasActiveFilters =
    searchTerm.trim() ||
    codeFilter.trim() ||
    isConfirmedFilter !== undefined ||
    userIdFilter ||
    templateIdFilter;

  const handleViewOrder = (orderId: number) => {
    router.push(`/orders/${orderId}`);
  };

  // Don't render interactive elements until client-side hydration is complete
  if (!isClient) {
    return (
      <div className='flex flex-1 flex-col'>
        <MainHeader />
        <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
          <div className='space-y-2 mb-4'>
            <h1 className='text-3xl font-medium tracking-tight flex items-center gap-2'>
              <ListOrdered className='h-8 w-8' />
              Orders
            </h1>
            <p className='text-muted-foreground'>
              Manage and track all orders in the system.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='flex flex-1 flex-col'>
      {/* Header */}
      <MainHeader />

      {/* Main Content */}
      <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
        <div className='space-y-2 mb-6'>
          <div className='flex items-center justify-between'>
            <div>
              <h1 className='text-2xl font-semibold tracking-tight flex items-center gap-2'>
                <ListOrdered className='h-6 w-6' />
                Orders
              </h1>
              <p className='text-sm text-muted-foreground mt-1'>
                Manage and track all orders in the system.
              </p>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className='grid gap-4 md:grid-cols-4 mb-6'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>
                Total Orders
              </CardTitle>
              <Package className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{orders?.length || 0}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Confirmed</CardTitle>
              <CheckCircle className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {orders?.filter(order => order.is_confirmed).length || 0}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Pending</CardTitle>
              <XCircle className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {orders?.filter(order => !order.is_confirmed).length || 0}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Users</CardTitle>
              <User className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {new Set(orders?.map(order => order.user_id)).size || 0}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <div className='flex flex-col lg:flex-row gap-4 mb-4'>
          {/* Search */}
          <div className='relative flex-1'>
            <Search className='absolute left-3 top-3 h-4 w-4 text-muted-foreground' />
            <Input
              placeholder='Search orders by name...'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className='pl-10'
            />
          </div>

          {/* Code Filter */}
          <div className='relative'>
            <Input
              placeholder='Filter by code...'
              value={codeFilter}
              onChange={e => setCodeFilter(e.target.value)}
              className='w-full lg:w-48'
            />
          </div>

          {/* Status Filter */}
          <Select
            value={
              isConfirmedFilter === undefined
                ? 'all'
                : isConfirmedFilter
                  ? 'confirmed'
                  : 'pending'
            }
            onValueChange={value => {
              if (value === 'all') {
                setIsConfirmedFilter(undefined);
              } else if (value === 'confirmed') {
                setIsConfirmedFilter(true);
              } else {
                setIsConfirmedFilter(false);
              }
            }}
          >
            <SelectTrigger className='w-full lg:w-40'>
              <SelectValue placeholder='Status' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All Status</SelectItem>
              <SelectItem value='confirmed'>Confirmed</SelectItem>
              <SelectItem value='pending'>Pending</SelectItem>
            </SelectContent>
          </Select>

          {/* Clear Filters */}
          {hasActiveFilters && (
            <Button
              variant='outline'
              onClick={clearFilters}
              className='flex items-center gap-2'
            >
              <X className='h-4 w-4' />
              Clear
            </Button>
          )}
        </div>

        {/* Loading State */}
        {loading && (
          <div className='flex items-center justify-center py-12'>
            <Loader2 className='h-8 w-8 animate-spin text-muted-foreground' />
          </div>
        )}

        {/* Table */}
        {!loading && (
          <div className='border rounded-lg overflow-hidden'>
            <div className='overflow-x-auto'>
              <Table className='min-w-full'>
                <TableHeader>
                  <TableRow>
                    <TableHead className='min-w-[150px] whitespace-nowrap'>
                      Name
                    </TableHead>
                    <TableHead className='min-w-[120px] whitespace-nowrap'>
                      Code
                    </TableHead>
                    <TableHead className='min-w-[180px] whitespace-nowrap'>
                      User
                    </TableHead>
                    <TableHead className='min-w-[120px] whitespace-nowrap'>
                      Template
                    </TableHead>
                    <TableHead className='min-w-[100px] whitespace-nowrap'>
                      Status
                    </TableHead>
                    <TableHead className='min-w-[100px] whitespace-nowrap'>
                      Duration
                    </TableHead>
                    <TableHead className='min-w-[80px] whitespace-nowrap text-center'>
                      Domains
                    </TableHead>
                    <TableHead className='min-w-[80px] whitespace-nowrap text-center'>
                      Projects
                    </TableHead>
                    <TableHead className='min-w-[120px] whitespace-nowrap'>
                      Created
                    </TableHead>
                    <TableHead className='min-w-[100px] whitespace-nowrap text-center'>
                      Actions
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {!orders || orders.length === 0 ? (
                    <TableRow>
                      <TableCell
                        colSpan={10}
                        className='text-center py-8 text-muted-foreground'
                      >
                        No orders found.{' '}
                        {hasActiveFilters && 'Try adjusting your filters.'}
                      </TableCell>
                    </TableRow>
                  ) : (
                    orders.map(order => (
                      <TableRow key={order.id}>
                        <TableCell className='font-medium whitespace-nowrap'>
                          <div
                            className='max-w-[150px] truncate'
                            title={order.name}
                          >
                            {order.name}
                          </div>
                        </TableCell>
                        <TableCell className='text-muted-foreground whitespace-nowrap'>
                          {order.code}
                        </TableCell>
                        <TableCell className='whitespace-nowrap'>
                          <div className='flex flex-col gap-1 max-w-[180px]'>
                            <span
                              className='font-medium truncate'
                              title={order.user.name}
                            >
                              {order.user.name}
                            </span>
                            <span
                              className='text-xs text-muted-foreground truncate'
                              title={order.user.email}
                            >
                              {order.user.email}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className='whitespace-nowrap'>
                          <Badge
                            variant='outline'
                            className='max-w-[120px] truncate'
                            title={order.template.name}
                          >
                            {order.template.name}
                          </Badge>
                        </TableCell>
                        <TableCell className='whitespace-nowrap'>
                          <Badge
                            variant={
                              order.is_confirmed ? 'default' : 'secondary'
                            }
                          >
                            {order.is_confirmed ? 'Confirmed' : 'Pending'}
                          </Badge>
                        </TableCell>
                        <TableCell className='whitespace-nowrap'>
                          {formatDuration(order.duration)}
                        </TableCell>
                        <TableCell className='text-center whitespace-nowrap'>
                          <Badge variant='outline'>
                            {order.order_domains?.length || 0}
                          </Badge>
                        </TableCell>
                        <TableCell className='text-center whitespace-nowrap'>
                          <Badge variant='outline'>
                            {order.projects?.length || 0}
                          </Badge>
                        </TableCell>
                        <TableCell className='text-muted-foreground whitespace-nowrap'>
                          {new Date(order.created_at).toLocaleDateString(
                            'en-US',
                            {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric',
                            }
                          )}
                        </TableCell>
                        <TableCell className='text-center whitespace-nowrap'>
                          <Button
                            variant='ghost'
                            size='sm'
                            onClick={() => handleViewOrder(order.id)}
                            className='flex items-center gap-2'
                          >
                            <Eye className='h-4 w-4' />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
