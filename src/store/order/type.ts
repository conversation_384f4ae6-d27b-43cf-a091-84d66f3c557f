export interface OrderStates {
  orders: DetailedOrderType[];
  myOrders: DetailedOrderType[];
  selectedOrder: DetailedOrderType | null;
  loading: boolean;
  myOrdersLoading: boolean;
  orderLoading: boolean;
  creating: boolean;
  creatingOrderDomain: boolean;
  updatingOrderDomainAvailability: boolean;
  deletingOrderDomain: boolean;
}

export interface OrderActions {
  setOrders: (orders: DetailedOrderType[]) => void;
  setMyOrders: (orders: DetailedOrderType[]) => void;
  setSelectedOrder: (order: DetailedOrderType | null) => void;
  setLoading: (loading: boolean) => void;
  setMyOrdersLoading: (loading: boolean) => void;
  setOrderLoading: (loading: boolean) => void;
  setCreating: (creating: boolean) => void;
  setCreatingOrderDomain: (creating: boolean) => void;
  setUpdatingOrderDomainAvailability: (updating: boolean) => void;
  setDeletingOrderDomain: (deleting: boolean) => void;
  fetchOrders: (params?: OrderQueryParams) => Promise<void>;
  fetchMyOrders: (params?: MyOrderQueryParams) => Promise<void>;
  fetchOrder: (id: number) => Promise<void>;
  createOrder: (data: CreateOrderRequest) => Promise<any>;
  createOrderDomain: (data: CreateOrderDomainRequest) => Promise<any>;
  updateOrderDomainAvailability: (
    id: number,
    data: UpdateOrderDomainAvailabilityRequest
  ) => Promise<any>;
  deleteOrderDomain: (id: number) => Promise<any>;
}

export interface OrderQueryParams {
  name?: string;
  code?: string;
  is_confirmed?: boolean;
  user_id?: number;
  template_id?: number;
}

export interface MyOrderQueryParams {
  name?: string;
  code?: string;
  is_confirmed?: boolean;
  template_id?: number;
}

export interface CreateOrderRequest {
  name: string;
  code: string;
  description: string;
  template_id: number;
  duration: number;
  order_domains: Array<{
    name: string;
  }>;
}

export interface CreateOrderDomainRequest {
  name: string;
  is_available: boolean;
  order_id: number;
}

export interface UpdateOrderDomainAvailabilityRequest {
  is_available: boolean;
}

export interface UserType {
  id: number;
  name: string;
  email: string;
  user_type: {
    id: number;
    name: string;
    description: string;
    is_active: boolean;
    is_admin: boolean;
    is_member: boolean;
    is_sale: boolean;
  };
}

export interface TemplateType {
  id: number;
  name: string;
  slug: string;
  is_active: boolean;
  type: string;
}

export interface OrderType {
  id: number;
  created_at: string;
  updated_at: string;
  name: string;
  code: string;
  description: string;
  is_confirmed: boolean;
  user_id: number;
  template_id: number;
  duration: number;
  user: UserType;
  template: TemplateType;
}

export interface IngressSpecType {
  id: number;
  host: string;
  path: string;
  port: number;
}

export interface ProjectType {
  id: number;
  name: string;
  slug: string;
  is_active: boolean;
  type: string;
  ingress_spec: IngressSpecType[];
}

export interface OrderDomainType {
  id: number;
  created_at?: string;
  updated_at?: string;
  name: string;
  is_available: boolean;
  order_id?: number;
}

export interface DetailedOrderType extends OrderType {
  order_domains: OrderDomainType[];
  projects: ProjectType[];
}

export interface OrderApiResponse {
  status: boolean;
  message: string;
  data: DetailedOrderType[];
}

export interface OrderDetailApiResponse {
  status: boolean;
  message: string;
  data: DetailedOrderType;
}
