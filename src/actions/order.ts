'use server';

import Http from '@/lib/http';

export interface OrderQueryParams {
  name?: string;
  code?: string;
  is_confirmed?: boolean;
  user_id?: number;
  template_id?: number;
}

export interface MyOrderQueryParams {
  name?: string;
  code?: string;
  is_confirmed?: boolean;
  template_id?: number;
}

export interface CreateOrderRequest {
  name: string;
  code: string;
  description: string;
  template_id: number;
  duration: number;
  order_domains: Array<{
    name: string;
  }>;
}

export interface CreateOrderDomainRequest {
  name: string;
  is_available: boolean;
  order_id: number;
}

export interface UpdateOrderDomainAvailabilityRequest {
  is_available: boolean;
}

export async function getOrders(params?: OrderQueryParams) {
  try {
    const queryParams = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value.toString());
        }
      });
    }

    const url = queryParams.toString()
      ? `/orders?${queryParams.toString()}`
      : '/orders';

    const response = await Http.get(url);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getMyOrders(params?: MyOrderQueryParams) {
  try {
    const queryParams = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value.toString());
        }
      });
    }

    const url = queryParams.toString()
      ? `/orders/my?${queryParams.toString()}`
      : '/orders/my';

    const response = await Http.get(url);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getOrder(id: number) {
  try {
    const response = await Http.get(`/orders/${id}`);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function createOrder(data: CreateOrderRequest) {
  try {
    const response = await Http.post('/orders', data);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function createOrderDomain(data: CreateOrderDomainRequest) {
  try {
    const response = await Http.post('/order-domains', data);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function updateOrderDomainAvailability(
  id: number,
  data: UpdateOrderDomainAvailabilityRequest
) {
  try {
    const response = await Http.patch(
      `/order-domains/${id}/availability`,
      data
    );
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function deleteOrderDomain(id: number) {
  try {
    const response = await Http.delete(`/order-domains/${id}`);
    // For DELETE requests, 204 status means success even if data is empty
    return {
      status: response.status === 204 || response.status === 200,
      data: response.data,
      message: 'Order domain deleted successfully',
    };
  } catch (error) {
    console.log(error);
    throw error;
  }
}
